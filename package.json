{"name": "highcapital-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"prettier": "npx prettier --write .", "dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky", "commit": "git-cz", "commit:all": "git add . && git-cz"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.12", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.540.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-is": "^19.1.1", "react-router-dom": "^7.8.1", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "zod": "^4.0.17"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.33.0", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-icons": "^3.0.0", "@vitejs/plugin-react": "^5.0.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "prettier": "^3.6.2", "shadcn": "^2.10.0", "tw-animate-css": "^1.3.7", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}