import { AuthFormLayout } from "../layouts";
import { BodyRegisterForm } from "../molecules/body-register-form";
import { FooterLoginForm } from "../molecules/footer-login-form";
import { HeaderFormLogin } from "../molecules/header-login-form";

const RegisterForm = () => {
  return (
    <AuthFormLayout
     imgUrl="https://img.freepik.com/free-photo/business-people-meeting_53876-15178.jpg?semt=ais_hybrid&w=740&q=80"
     imagePosition="left"
    >
        <HeaderFormLogin />
        <BodyRegisterForm onSubmit={() => {}}/>
        <FooterLoginForm />

    </AuthFormLayout>
  )
};

export default RegisterForm;
