import { DataExemple } from '@/constants/DataExemple';
import { Card, CardContent, CardHeader, CardTitle } from '../atoms';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, Tooltip, XAxis, YAxis } from 'recharts';

const Chart = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-center text-xl font-bold">
          montly revenue{' '}
        </CardTitle>
      </CardHeader>
      <CardContent className=" flex items-center justify-center ">
        <BarChart width={400} height={300} data={DataExemple}>
          <CartesianGrid strokeDasharray={'3 3'} />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Bar dataKey={'revenue'} radius={[4, 4, 0, 0]} />
        </BarChart>
      </CardContent>
    </Card>
  );
};

export default Chart;
