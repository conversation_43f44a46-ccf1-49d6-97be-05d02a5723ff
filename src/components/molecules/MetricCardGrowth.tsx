import { Bar<PERSON><PERSON>3, TrendingUp } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../atoms';

const MetricCardGrowth = () => {
  return (
    <Card className="mt-4 h-[250px] w-1/2 p-4">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">growlth Rate</CardTitle>
        <span className="text-xs font-semibold text-green-800 flex items-center gap-1">
          4 % <TrendingUp className="h-3 w-3" />
        </span>
      </CardHeader>
      <CardContent>
        <h1 className="text-md font-medium flex flex-row gap-4">
          Steady performance increase <BarChart3 className="h-5 w-5" />{' '}
        </h1>
        <p className="text-xs text-muted-foreground">
          {' '}
          Meets growth projections
        </p>
      </CardContent>
    </Card>
  );
};

export default MetricCardGrowth;
