import type { ReactNode } from 'react';
import { Button } from '../atoms';

interface ButtonWithLogoProps extends React.ComponentProps<'button'> {
  icon: ReactNode;
  text: string;
}

export const ButtonWithIcon = ({
  icon,
  text,
  ...rest
}: ButtonWithLogoProps) => (
  <Button
    variant="outline"
    className="border border-gray-200 shadow-sm mx-auto"
    {...rest}
  >
    <div className="flex items-center justify-center h-10">
      <span className="sr-only">{text}</span>
      {icon}
      <span className="ml-2.5 font-medium">{text}</span>
    </div>
  </Button>
);
