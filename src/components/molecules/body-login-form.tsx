import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { FaApple, FaGoogle } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import * as z from 'zod';
import { Button } from '../atoms';
import { ButtonWithIcon } from './button-with-icon';
import { InputLabel } from './input-label';

interface BodyLoginFormProps {
  onSubmit: (data: loginSchemaProps) => void;
}

const loginSchema = z.object({
  email: z
    .string()
    .nonempty("O email é obrigatório")
    .email("Email inválido"),
  password: z
    .string()
    .nonempty("A senha é obrigatória")
    .min(6, "A senha deve ter pelo menos 6 caracteres"),
});

export type loginSchemaProps = z.infer<typeof loginSchema>;

export const BodyLoginForm = ({ onSubmit }: BodyLoginFormProps) => {

  const navigate = useNavigate();

  const {
    handleSubmit,
    formState: { errors },
    register,
  } = useForm<loginSchemaProps>({
    resolver: zodResolver(loginSchema),
  });

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col items-center w-full grow-1 p-6 mt-14"
    >
      <h1 className="text-3xl font-semibold">Welcome Back</h1>
      <h6 className="text-xs text-gray-500 mt-4">
        Enter your email and password to access your account
      </h6>

      <div className="w-full h-full mt-10 flex flex-col px-12 ">
        <InputLabel
          type="email"
          label="Email"
          placeholder="Email"
          {...register("email")}
        />
        {errors.email && (
          <p className="text-red-500 text-[10px] mt-1">
            {errors.email.message}
          </p>
        )}

        <InputLabel
          type="password"
          label="Password"
          placeholder="Password"
          className="mt-6"
          {...register("password")}
        />
        {errors.password && (
          <p className="text-red-500 text-[10px] mt-1">
            {errors.password.message}
          </p>
        )}

        <div className="w-full flex justify-end mt-3">
          <Link
            to="/forgot-password"
            className="text-xs text-[#2ec0df] font-semibold"
          >
            Forgot Your Password?
          </Link>
        </div>

        <Button
          type="submit"
          className="mt-6 bg-gradient-to-r text-white text-xs bg-[#2ec0df] font-medium hover:bg-[#2593ac]"
        >
          Login
        </Button>

        <div className="flex items-center gap-2 text-gray-400 text-xs mt-6">
          <div className="flex-1 border-t border-gray-200" />
          <span>or continue with</span>
          <div className="flex-1 border-t border-gray-200" />
        </div>

        <div className="flex gap-2 mt-6">
          <ButtonWithIcon
            icon={<FaGoogle className="mr-2" />}
            text="Login with Google"
          />
          <ButtonWithIcon
            icon={<FaApple className="mr-2" />}
            text="Login with Apple"
          />
        </div>

        <div className="flex justify-center items-center text-xs gap-1 mt-2">
          <span>Don't have account?</span>
          <h1 onClick={() => navigate('/auth/register')}  className="text-xs text-[#2ec0df] font-semibold">
            Register here
          </h1>
        </div>
      </div>
    </form>
  );
};
