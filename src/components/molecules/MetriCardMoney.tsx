import { TrendingUp, Wallet } from 'lucide-react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '../atoms';

const MetricCardMoney = () => {
  return (
    <Card className="w-1/2 h-[250px] mt-4 p-8">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
        <span className="text-xs font-semibold text-green-800 flex items-center gap-1">
          +12.5% <TrendingUp className="h-3 w-3" />
        </span>
      </CardHeader>
      <CardContent className='flex px-5 py-4 flex-col justify-center'>
        <h1 className="text-2xl flex flex-row gap-2 font-bold ">
          {' '}
          <Wallet size={20} className="mt-2 " /> $1,250.00
        </h1>
        <p className="text-xs text-muted-foreground">Trending up this month</p>
      </CardContent>
    </Card>
  );
};

export default MetricCardMoney;
