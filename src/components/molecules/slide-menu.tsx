import {
  BarChart2,
  Calendar,
  Eye,
  FilePlus,
  FileText,
  Funnel,
  Mail,
  Menu,
  Repeat,
  TrendingUp,
  User,
} from 'lucide-react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '../atoms/sheet';
import Feature from '../atoms/Feauture';

const SlideMenu = () => {
  return (
    <Sheet>
      <SheetTrigger>
        <Menu className="w-6 h-6" />
      </SheetTrigger>
      <SheetContent side="left" className="h-full p-4">
        <SheetHeader>
          <SheetTitle>
            <h1 className="text-3xl font-bold ">Quick acess</h1>
          </SheetTitle>
        </SheetHeader>
        <div className="flex flex-col gap-4  mt-4 overflow-y-auto max-h-[calc(100vh-150px)]">
          <Feature
            name="Smart Copy Generator"
            icon={<FileText className="w-6 h-6" />}
          />
          <Feature
            name="Lead Scorer"
            icon={<TrendingUp className="w-6 h-6" />}
          />
          <Feature name="Email Campaign Optimizer" icon={<Mail />} />
          <Feature
            name="Sales Insights Dashboard"
            icon={<BarChart2 className="w-6 h-6" />}
          />
          <Feature name="Client Persona Builder" icon={<User />} />
          <Feature
            name="Social Media Scheduler"
            icon={<Calendar className="w-6 h-6" />}
          />
          <Feature
            name="Automated Proposal Generator"
            icon={<FilePlus className="w-6 h-6" />}
          />
          <Feature
            name="Conversion Funnel Analyzer"
            icon={<Funnel className="w-6 h-6" />}
          />
          <Feature
            name="Competitor Tracker"
            icon={<Eye className="w-6 h-6" />}
          />
          <Feature
            name="Automated Follow-Ups"
            icon={<Repeat className="w-6 h-6" />}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default SlideMenu;
