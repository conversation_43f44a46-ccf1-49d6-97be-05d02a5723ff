import { HeaderFormLogin } from "@/components/molecules/header-login-form";
import { Link } from "react-router-dom";
import { FaRegSadCry } from "react-icons/fa";


const NotFoundPage = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-200 text-center px-4">
      <HeaderFormLogin />
      <h1 className="text-6xl mt-8 font-bold text-[var(--hc-scooter)]">404</h1>
      <p className="mt-2 flex-row flex gap-4 text-lg text-gray-600">Oops! Page not found <FaRegSadCry className="mt-1" /> .</p>
      
      <Link
        to="/"
        className="mt-6 px-6 py-4 rounded-2xl text-white font-medium shadow-md  hover:bg-[#30636f]"
        style={{ backgroundColor: "var(--hc-scooter)" }}
      >
        Return to Home
      </Link>
    </div>
  );
};

export default NotFoundPage;
