import { ModeToggle } from '@/components';

import SlideMenu from '@/components/molecules/slide-menu';
import Cards from '@/components/organisms/Cards';
import Chart from '@/components/organisms/Chart';
import Profile from '@/components/organisms/Profile';
import { Zap } from 'lucide-react';

const DashboardPage = () => {
  return (
    <>
      <Profile />
      <div className="flex items-center justify-between w-full px-4 mt-4">
        <SlideMenu />
        <h1 className="text-3xl font-bold flex flex-row">
          dashboard <Zap className="mt-1 ml-2" />{' '}
        </h1>
        <ModeToggle />
      </div>
      <Cards />
      <Chart />
      </>
  );
};

export default DashboardPage;
