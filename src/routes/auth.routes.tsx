import LoginPage from "@/pages/login-page";
import RegisterPage from "@/pages/register-page";
import { Navigate, type RouteObject } from "react-router-dom";

export const authRoutes: RouteObject[] = [
    {
        path: '',
        element: <Navigate to="login" replace />
    },
    {
        path: 'login',
        element: <LoginPage />,
    },
    {
        path: 'register',
        element: <RegisterPage />
    }

];